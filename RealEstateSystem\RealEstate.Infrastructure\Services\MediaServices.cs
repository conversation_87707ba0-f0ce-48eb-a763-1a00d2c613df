﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;
using RealEstate.Domain.Entities;
using RealEstate.Domain.Interfaces;
using Shared.Enums;
using Shared.Results;
using System.IO;

namespace RealEstate.Infrastructure.Services
{
    public class MediaServices : IMediaServices
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IMapper _mapper;
        private readonly IImageProcessingService _imageProcessingService;
        private readonly IPropertyService _propertyService;
        private readonly IFileStorageService _fileStorageService;

        public MediaServices(IUnitOfWork unitOfWork, IMapper mapper, IImageProcessingService imageProcessingService, IPropertyService propertyService, IFileStorageService fileStorageService)
        {
            _unitOfWork = unitOfWork;
            _mapper = mapper;
            _imageProcessingService = imageProcessingService;
            _propertyService = propertyService;
            _fileStorageService = fileStorageService;
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> GetAllMediaAsync(List<Guid> mediaIds)
        {
            var medias = await _unitOfWork.PropertyMedias.FindAsync(x => mediaIds.Contains(x.Id) && x.PropertyID == null);
            return Result<IEnumerable<PropertyMediaDto>>.Success(_mapper.Map<IEnumerable<PropertyMediaDto>>(medias));
        }

        public async Task<Result<PropertyMediaDto>> GetMediaByIdAsync(Guid id)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id, true, null);
            if (media == null)
            {
                return Result<PropertyMediaDto>.Failure("Media not found.", ErrorType.NotFound);
            }
            return Result<PropertyMediaDto>.Success(_mapper.Map<PropertyMediaDto>(media));
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> GetAllMediaAsync()
        {
            var medias = await _unitOfWork.PropertyMedias.GetAllAsync();
            return Result<IEnumerable<PropertyMediaDto>>.Success(_mapper.Map<IEnumerable<PropertyMediaDto>>(medias));
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> GetMediaByPropertyIdAsync(Guid propertyId)
        {
            var medias = await _unitOfWork.PropertyMedias.FindAsync(m => m.PropertyID == propertyId);
            return Result<IEnumerable<PropertyMediaDto>>.Success(_mapper.Map<IEnumerable<PropertyMediaDto>>(medias));
        }

        public async Task<Result<PropertyMediaDto>> CreateMediaAsync(CreateMediaDto mediaDto)
        {
            var media = _mapper.Map<PropertyMedia>(mediaDto);
            await _unitOfWork.PropertyMedias.AddAsync(media);
            await _unitOfWork.SaveChangesAsync();
            return Result<PropertyMediaDto>.Success(_mapper.Map<PropertyMediaDto>(media));
        }

        public async Task<Result<PropertyMediaDto>> UpdateMediaAsync(Guid id, CreateMediaDto mediaDto)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result<PropertyMediaDto>.Failure("Media not found.", ErrorType.NotFound);
            }

            _mapper.Map(mediaDto, media);
            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result<PropertyMediaDto>.Success(_mapper.Map<PropertyMediaDto>(media));
        }

        public async Task<Result> DeleteMediaAsync(Guid id)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result.Failure("Media not found.", ErrorType.NotFound);
            }

            _unitOfWork.PropertyMedias.Remove(media);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public Task<Result<List<Guid>>> UpdateMediaAsync(Guid propertyId, List<Guid> mediaIds)
        {
            throw new NotImplementedException();
        }

        public async Task<Result<Guid>> UpdateMediaAsync(Guid mediaId, Guid propertyId, string filePath, PropertyMediaDto propertyMedia)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(mediaId);
            if (media == null)
            {
                return Result<Guid>.Failure("Media not found.", ErrorType.NotFound);
            }

            media.FilePath = filePath;
            media.PropertyID = propertyId;
            media.Caption = propertyMedia.Caption;
            media.IsAvatar = propertyMedia.IsAvatar;

            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result<Guid>.Success(mediaId);
        }

        public async Task<Result<IEnumerable<PropertyMediaDto>>> UploadPropertyImagesAsync(Guid? propertyId, IFormFileCollection files, string scheme, string host)
        {
            if (files == null || files.Count == 0)
            {
                return Result<IEnumerable<PropertyMediaDto>>.Failure("No files uploaded.", ErrorType.Validation);
            }

            // Validate property exists if propertyId is provided
            if (propertyId != null && propertyId != Guid.Empty)
            {
                var propertyResult = await _propertyService.GetPropertyByIdAsync(propertyId.Value);
                if (!propertyResult.IsSuccess)
                {
                    return Result<IEnumerable<PropertyMediaDto>>.Failure("Property not found.", ErrorType.NotFound);
                }
            }

            // Get the appropriate upload folder
            string uploadFolder = _fileStorageService.GetPropertyImageFolder(propertyId);

            // Ensure folder exists
            var folderResult = await _fileStorageService.EnsureFolderExistsAsync(uploadFolder);
            if (!folderResult.IsSuccess)
            {
                return Result<IEnumerable<PropertyMediaDto>>.Failure(folderResult.Error, folderResult.ErrorType);
            }

            var uploadedFiles = new List<PropertyMediaDto>();

            foreach (var file in files)
            {
                if (!file.ContentType.StartsWith("image/")) continue;

                var fileId = Guid.NewGuid();
                var extension = Path.GetExtension(file.FileName);
                var tempFileName = $"temp_{fileId}{extension}";

                // Upload the temporary file
                var uploadResult = await _fileStorageService.UploadFileAsync(file, uploadFolder, tempFileName);
                if (!uploadResult.IsSuccess)
                {
                    continue; // Skip this file and continue with others
                }

                var tempFilePath = uploadResult.Value;

                try
                {
                    var baseFilename = fileId.ToString();
                    var watermarkText = "YEZ HOME";
                    var processedImages = await _imageProcessingService.GenerateImageSizesAsync(tempFilePath, uploadFolder, baseFilename, watermarkText);

                    string publicUrl = _fileStorageService.GeneratePublicUrl(processedImages["original"], scheme, host);
                    var media = new CreateMediaDto
                    {
                        Id = fileId,
                        PropertyID = propertyId,
                        MediaType = file.ContentType,
                        MediaURL = publicUrl,
                        FilePath = processedImages["original"],
                        ThumbnailPath = processedImages["thumbnail"],
                        SmallPath = processedImages["small"],
                        MediumPath = processedImages["medium"],
                        LargePath = processedImages["large"],
                        UploadedAt = DateTime.UtcNow
                    };

                    var createdMediaResult = await CreateMediaAsync(media);

                    if (createdMediaResult.Value != null)
                        uploadedFiles.Add(createdMediaResult.Value);
                }
                finally
                {
                    // Clean up temporary file
                    await _fileStorageService.DeleteFileAsync(tempFilePath);
                }
            }

            return Result<IEnumerable<PropertyMediaDto>>.Success(uploadedFiles);
        }

        public async Task<Result> UpdateMediaCaptionAsync(Guid id, string caption)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result.Failure("Media not found.", ErrorType.NotFound);
            }

            media.Caption = caption;
            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public async Task<Result> UpdateMediaIsAvatarAsync(Guid id, bool isAvatar)
        {
            var media = await _unitOfWork.PropertyMedias.GetByIdAsync(id);
            if (media == null)
            {
                return Result.Failure("Media not found.", ErrorType.NotFound);
            }

            if (media.PropertyID == null)
            {
                return Result.Failure("This media is not associated with any property.", ErrorType.Validation);
            }

            if (isAvatar)
            {
                var otherMedia = await _unitOfWork.PropertyMedias.FindAsync(
                    m => m.PropertyID == media.PropertyID && m.Id != id && m.IsAvatar == true);
                foreach (var item in otherMedia)
                {
                    item.IsAvatar = false;
                    _unitOfWork.PropertyMedias.Update(item);
                }
            }

            media.IsAvatar = isAvatar;
            _unitOfWork.PropertyMedias.Update(media);
            await _unitOfWork.SaveChangesAsync();
            return Result.Success();
        }

        public string GetMediaPath(PropertyMediaDto media, string size)
        {
            switch (size?.ToLowerInvariant())
            {
                case "thumbnail":
                    return !string.IsNullOrEmpty(media.ThumbnailURL) ? media.ThumbnailURL! : media.FilePath!;
                case "small":
                    return !string.IsNullOrEmpty(media.SmallURL) ? media.SmallURL! : media.FilePath!;
                case "medium":
                    return !string.IsNullOrEmpty(media.MediumURL) ? media.MediumURL! : media.FilePath!;
                case "large":
                    return !string.IsNullOrEmpty(media.LargeURL) ? media.LargeURL! : media.FilePath!;
                default:
                    return media.FilePath!;
            }
        }
    }
}