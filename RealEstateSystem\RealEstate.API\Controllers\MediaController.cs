﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using RealEstate.Application.DTO;
using RealEstate.Application.Interfaces;

namespace RealEstate.API.Controllers
{
    [Route("[controller]")]
    [Authorize]
    public class MediaController : BaseController
    {
        private readonly IMediaServices _mediaServices;

        public MediaController(IMediaServices mediaServices)
        {
            _mediaServices = mediaServices;
        }

        [HttpGet("{fileId}")]
        [AllowAnonymous]
        public async Task<IActionResult> GetMedia(Guid fileId, [FromQuery] string size = null)
        {
            var mediaResult = await _mediaServices.GetMediaByIdAsync(fileId);
            if (!mediaResult.IsSuccess)
                return HandleResult(mediaResult);

            var media = mediaResult.Value;
            string filePath = _mediaServices.GetMediaPath(media, size);

            if (string.IsNullOrEmpty(filePath) || !System.IO.File.Exists(filePath))
            {
                return NotFound(new { Message = "Media file not found" });
            }

            var fileStream = new FileStream(filePath, FileMode.Open, FileAccess.Read);
            return File(fileStream, media.MediaType!);
        }

        [HttpPut("update-caption")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateCaption([FromBody] UpdatePropertyMediaCaptionDto updateDto)
        {
            var result = await _mediaServices.UpdateMediaCaptionAsync(updateDto.Id, updateDto.Caption);
            return HandleResult(result);
        }

        [HttpPut("update-is-avatar")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> UpdateIsAvatar([FromBody] UpdatePropertyMediaIsAvatarDto updateDto)
        {
            var result = await _mediaServices.UpdateMediaIsAvatarAsync(updateDto.Id, updateDto.IsAvatar);
            return HandleResult(result);
        }

        [HttpDelete("{id}")]
        [Authorize(Policy = "UserExists")]
        public async Task<IActionResult> DeleteMedia(Guid id)
        {
            var result = await _mediaServices.DeleteMediaAsync(id);
            return HandleResult(result);
        }
    }
}
