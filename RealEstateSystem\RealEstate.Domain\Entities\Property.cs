using RealEstate.Domain.Common;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;

namespace RealEstate.Domain.Entities
{
    public class Property : BaseEntityWithAuditable
    {
        public Guid OwnerID { get; set; }
        public string? PropertyType { get; set; }
        public string? PostType { get; set; }
        public string Name { get; set; } = string.Empty;
        public string Slug { get; set; } = string.Empty;
        public int? CityId { get; set; }
        public int? DistrictId { get; set; }
        public int? StreetId { get; set; }
        public int? WardId { get; set; }
        public string Address { get; set; } = string.Empty;
        public decimal? Area { get; set; }
        public decimal Price { get; set; }
        public decimal Latitude { get; set; }
        public decimal Longitude { get; set; }
        public string? VideoUrl { get; set; }
        public int? Floors { get; set; }
        public int? Rooms { get; set; }
        public int? Toilets { get; set; }
        public string? Direction { get; set; }
        public string? BalconyDirection { get; set; }
        public string? Legality { get; set; }
        public string? Interior { get; set; }
        public int? Width { get; set; }
        public int? RoadWidth { get; set; }
        public string Description { get; set; } = string.Empty;
        public string? Overview { get; set; } = string.Empty;
        public string? PlaceData { get; set; } = string.Empty;
        public string? Policies { get; set; } = string.Empty;
        public string? Neighborhood { get; set; } = string.Empty;
        public string Status { get; set; } = EnumValues.PropertyStatus.Draft.ToString();
        [Column(TypeName = "numeric(20,2)")]
        public decimal PostPrice { get; set; } = 55000;
        public bool IsHighlighted { get; set; } = false;
        public bool IsAutoRenew { get; set; } = false;
        public DateTime ExpiresAt { get; set; } = DateTime.UtcNow.AddDays(10);
        public int? UpdateRemainingTimes { get; set; }
        public int? RenewalCount { get; set; } = 0;
        public long Code { get; set; }

        public AppUser Owner { get; set; }
        public ICollection<PropertyMedia> PropertyMedia { get; set; } = new List<PropertyMedia>();
        public ICollection<UserFavorite> UserFavorites { get; set; } = new List<UserFavorite>();
        public ICollection<PropertyStatusLog> PropertyStatusLogs { get; set; } = new List<PropertyStatusLog>();
    }
} 