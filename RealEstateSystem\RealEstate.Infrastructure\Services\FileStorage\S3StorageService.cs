using Amazon.S3;
using Amazon.S3.Model;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using RealEstate.Application.Interfaces;
using Shared.Enums;
using Shared.Results;

namespace RealEstate.Infrastructure.Services.FileStorage
{
    /// <summary>
    /// AWS S3 storage implementation
    /// </summary>
    public class S3StorageService : IFileStorageService
    {
        private readonly IAmazonS3 _s3Client;
        private readonly ILogger<S3StorageService> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _bucketName;
        private readonly string _region;

        public S3StorageService(IAmazonS3 s3Client, ILogger<S3StorageService> logger, IConfiguration configuration)
        {
            _s3Client = s3Client;
            _logger = logger;
            _configuration = configuration;
            _bucketName = _configuration["AWS:S3:BucketName"] ?? "yezhome-media";
            _region = _configuration["AWS:Region"] ?? "us-east-1";
        }

        public async Task<Result<string>> UploadFileAsync(IFormFile file, string folder, string fileName)
        {
            try
            {
                if (file == null || file.Length == 0)
                {
                    return Result<string>.Failure("No file provided.", ErrorType.Validation);
                }

                var key = $"{folder}/{fileName}".Replace("\\", "/");

                using var stream = file.OpenReadStream();
                var request = new PutObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key,
                    InputStream = stream,
                    ContentType = file.ContentType,
                    ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256
                };

                var response = await _s3Client.PutObjectAsync(request);

                if (response.HttpStatusCode == System.Net.HttpStatusCode.OK)
                {
                    _logger.LogInformation("File uploaded successfully to S3: {Key}", key);
                    return Result<string>.Success(key);
                }

                return Result<string>.Failure("Failed to upload file to S3.", ErrorType.Internal);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading file to S3: {Folder}/{FileName}", folder, fileName);
                return Result<string>.Failure($"Failed to upload file to S3: {ex.Message}", ErrorType.Internal);
            }
        }

        public async Task<Result> MoveFileAsync(string sourcePath, string destinationPath, bool overwrite = true)
        {
            try
            {
                var sourceKey = sourcePath.Replace("\\", "/");
                var destinationKey = destinationPath.Replace("\\", "/");

                // Check if source exists
                if (!await FileExistsAsync(sourceKey))
                {
                    return Result.Failure("Source file does not exist in S3.", ErrorType.NotFound);
                }

                // Copy object to new location
                var copyRequest = new CopyObjectRequest
                {
                    SourceBucket = _bucketName,
                    SourceKey = sourceKey,
                    DestinationBucket = _bucketName,
                    DestinationKey = destinationKey,
                    ServerSideEncryptionMethod = ServerSideEncryptionMethod.AES256
                };

                var copyResponse = await _s3Client.CopyObjectAsync(copyRequest);

                if (copyResponse.HttpStatusCode == System.Net.HttpStatusCode.OK)
                {
                    // Delete the source file
                    var deleteResult = await DeleteFileAsync(sourceKey);
                    if (deleteResult.IsSuccess)
                    {
                        _logger.LogInformation("File moved in S3 from {SourceKey} to {DestinationKey}", sourceKey, destinationKey);
                        return Result.Success();
                    }
                    else
                    {
                        _logger.LogWarning("File copied to S3 but failed to delete source: {SourceKey}", sourceKey);
                        return Result.Success(); // Consider this a success since the file was copied
                    }
                }

                return Result.Failure("Failed to copy file in S3.", ErrorType.Internal);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error moving file in S3 from {SourcePath} to {DestinationPath}", sourcePath, destinationPath);
                return Result.Failure($"Failed to move file in S3: {ex.Message}", ErrorType.Internal);
            }
        }

        public async Task<Result> DeleteFileAsync(string filePath)
        {
            try
            {
                var key = filePath.Replace("\\", "/");

                var request = new DeleteObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key
                };

                var response = await _s3Client.DeleteObjectAsync(request);

                if (response.HttpStatusCode == System.Net.HttpStatusCode.NoContent)
                {
                    _logger.LogInformation("File deleted from S3: {Key}", key);
                    return Result.Success();
                }

                return Result.Failure("Failed to delete file from S3.", ErrorType.Internal);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error deleting file from S3: {FilePath}", filePath);
                return Result.Failure($"Failed to delete file from S3: {ex.Message}", ErrorType.Internal);
            }
        }

        public async Task<bool> FileExistsAsync(string filePath)
        {
            try
            {
                var key = filePath.Replace("\\", "/");

                var request = new GetObjectMetadataRequest
                {
                    BucketName = _bucketName,
                    Key = key
                };

                await _s3Client.GetObjectMetadataAsync(request);
                return true;
            }
            catch (AmazonS3Exception ex) when (ex.StatusCode == System.Net.HttpStatusCode.NotFound)
            {
                return false;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking if file exists in S3: {FilePath}", filePath);
                return false;
            }
        }

        public Task<Result> EnsureFolderExistsAsync(string folderPath)
        {
            // S3 doesn't have folders, so this is always successful
            return Task.FromResult(Result.Success());
        }

        public string GeneratePublicUrl(string filePath, string scheme, string host)
        {
            // For S3, we can generate a CloudFront URL or direct S3 URL
            var key = filePath.Replace("\\", "/");
            
            // Extract the file ID from the key for the media endpoint
            var fileName = Path.GetFileNameWithoutExtension(key);
            var fileId = fileName.Split('_')[0];
            
            // Return the media endpoint URL (the MediaController will handle S3 retrieval)
            return $"{scheme}://{host}/media/{fileId}";
        }

        public string GetPropertyImageFolder(Guid? propertyId)
        {
            if (propertyId != null && propertyId != Guid.Empty)
            {
                return $"PropertyImages/{propertyId.Value}";
            }
            else
            {
                return $"Temp/{DateTime.Today:yyyyMMdd}";
            }
        }

        public string GenerateTempFilePath(string folder, Guid fileId, string extension)
        {
            return $"{folder}/temp_{fileId}{extension}";
        }

        /// <summary>
        /// Get a pre-signed URL for direct file access (useful for MediaController)
        /// </summary>
        public async Task<string> GetPreSignedUrlAsync(string key, TimeSpan expiration)
        {
            try
            {
                var request = new GetPreSignedUrlRequest
                {
                    BucketName = _bucketName,
                    Key = key.Replace("\\", "/"),
                    Verb = HttpVerb.GET,
                    Expires = DateTime.UtcNow.Add(expiration)
                };

                return await _s3Client.GetPreSignedURLAsync(request);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error generating pre-signed URL for S3 key: {Key}", key);
                throw;
            }
        }

        /// <summary>
        /// Get file stream from S3 (useful for MediaController)
        /// </summary>
        public async Task<Stream> GetFileStreamAsync(string key)
        {
            try
            {
                var request = new GetObjectRequest
                {
                    BucketName = _bucketName,
                    Key = key.Replace("\\", "/")
                };

                var response = await _s3Client.GetObjectAsync(request);
                return response.ResponseStream;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting file stream from S3: {Key}", key);
                throw;
            }
        }
    }
}
