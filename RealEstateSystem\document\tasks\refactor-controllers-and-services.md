# Kế hoạch Refactor Controllers và Services

Dựa trên `response-rule.mdc` và tham kh<PERSON>o `BlogController.cs`, chúng ta sẽ tiến hành refactor các controller và service sau đây.

Mục tiêu chính:
- **Controller**: Chỉ chịu trách nhiệm điều hướng request, gọi service và trả về `IActionResult` thông qua `HandleResult(result)`.
- **Service**: Xử lý toàn bộ logic nghiệp vụ, trả về đối tượng `Result` hoặc `Result<T>`.

---

## Danh sách công việc

### 1. NotificationController & INotificationService [ĐÃ HOÀN THÀNH]

- **`NotificationController.cs`**:
    - [x] `GetAll`:
        - [x] Chuyển logic filter dựa trên `NotificationPreference` vào `NotificationService`.
        - [x] T<PERSON> thế `try-catch` và các `return Ok()`, `return BadRequest()`, `return StatusCode()` bằng `return HandleResult(result)`.
        - [x] Service `GetNotificationsForUserAsync` nên trả về `Result<PagedResultDto<NotificationDto>>`.
    - [x] `GetById`:
        - [x] Áp dụng `HandleResult`. Service `GetNotificationByIdAsync` trả về `Result<NotificationDto>`.
    - [x] `GetByType`:
        - [x] Áp dụng `HandleResult`. Service `GetNotificationsByTypeAndUserAsync` trả về `Result<PagedResultDto<NotificationDto>>`.
    - [x] `GetUserNotifications`:
        - [x] Chuyển logic filter vào service.
        - [x] Áp dụng `HandleResult`. Service `GetAllNotificationsForUserAsync` trả về `Result<PagedResultDto<NotificationDto>>`.
    - [x] `MarkAsRead`:
        - [x] Áp dụng `HandleResult`. Service `MarkAsReadAsync` trả về `Result`.
    - [x] `MarkAllAsRead`:
        - [x] Áp dụng `HandleResult`. Service `MarkAllAsReadAsync` trả về `Result`.
    - [x] `Delete`:
        - [x] Áp dụng `HandleResult`. Service `DeleteNotificationAsync` trả về `Result`.
    - [x] `GetUnreadCount`:
        - [x] Áp dụng `HandleResult`. Service `GetUnreadNotificationCountAsync` trả về `Result<int>`.
    - [x] `MarkMultipleAsRead`:
        - [x] Chuyển logic lặp và gọi service vào `NotificationService`.
        - [x] Áp dụng `HandleResult`. Service `MarkMultipleAsReadAsync` trả về `Result`.

### 2. PropertyController & IPropertyService & IMediaServices [ĐÃ HOÀN THÀNH]

- **`PropertyController.cs`**:
    - [x] `GetPropertyById`: Áp dụng `HandleResult`. Service `GetPropertyByIdAsync` trả về `Result<PropertyDto>`.
    - [x] `CreateProperty`: Áp dụng `HandleResult`. Service `CreatePropertyAsync` trả về `Result<PropertyDto>`.
    - [x] `UpdateProperty`: Áp dụng `HandleResult`. Service `UpdatePropertyAsync` trả về `Result<PropertyDto>`.
    - [x] `DeleteProperty`: Áp dụng `HandleResult`. Service `DeletePropertyAsync` trả về `Result`.
    - [x] `DeleteProperties`: Áp dụng `HandleResult`. Service `DeletePropertiesAsync` trả về `Result`.
    - [x] `UpdateStatus`: Áp dụng `HandleResult`. Service `UpdateStatusAsync` trả về `Result`.
    - [x] `UpdateStatusBulk`: Áp dụng `HandleResult`. Service `UpdateStatusBulkAsync` trả về `Result`.
    - [x] `UpdateHighlight`: Áp dụng `HandleResult`. Service `UpdateHighlightWithPaymentAsync` trả về `Result<PropertyDto>`.
    - [x] `UpdateHighlightBulk`: Áp dụng `HandleResult`. Service `UpdateHighlightBulkWithPaymentAsync` trả về `Result<BulkHighlightResultDto>`.
    - [x] `GetAllPropertiesByUser`: Áp dụng `HandleResult`. Service `GetPropertyByUserWithStatusAsync` trả về `Result<PagedResultDto<PropertyDto>>`.
    - [x] `UploadPropertyImages`:
        - [x] Chuyển toàn bộ logic xử lý file (tạo thư mục, lưu file, xử lý ảnh, tạo DTO) vào `MediaService`.
        - [x] Áp dụng `HandleResult`. `MediaService` cần có method mới trả về `Result<IEnumerable<PropertyMediaDto>>`.
    - [x] `VerifyPropertyRemainingTimes`: Áp dụng `HandleResult`. Service `VerifyPropertyRemainingTimes` trả về `Result<int>`.
    - [x] `GetPropertyCountStats`: Áp dụng `HandleResult`. Service `GetPropertyCountStatsByUserAsync` trả về `Result<PropertyCountStatsDto>`.
    - [x] `SearchProperties`: Áp dụng `HandleResult`. Service `SearchPropertiesAsync` trả về `Result<PagedResultDto<PropertyDto>>`.
    - [x] `GetNearbyProperties`: Áp dụng `HandleResult`. Service `SearchPropertiesAsync` trả về `Result<PagedResultDto<PropertyDto>>`.
    - [x] `RenewProperty`:
        - [x] Chuyển toàn bộ logic (kiểm tra, tính phí, gọi wallet service, cập nhật) vào `PropertyService`.
        - [x] Áp dụng `HandleResult`. Service `RenewPropertyAsync` trả về `Result<PropertyDto>`.

### 3. WalletTransactionController & IWalletService [ĐÃ HOÀN THÀNH]

- **`WalletTransactionController.cs`**:
    - [x] `GetTransactions`: Áp dụng `HandleResult`. Service trả về `Result<IEnumerable<WalletTransactionDto>>`.
    - [x] `GetBalance`: Áp dụng `HandleResult`. Service trả về `Result<WalletBalanceDto>`.
    - [x] `TopUpWallet`: Áp dụng `HandleResult`. Service trả về `Result<WalletTransactionDto>`.
    - [x] `SpendFromWallet`: Áp dụng `HandleResult`. Service trả về `Result<WalletTransactionDto>`.
    - [x] `GetTransactionById`: Áp dụng `HandleResult`. Service trả về `Result<WalletTransactionDto>`.
    - [x] `GetUserPendingTransactions`: Áp dụng `HandleResult`. Service trả về `Result<IEnumerable<WalletTransactionDto>>`.
    - [x] `SearchTransactions`: Áp dụng `HandleResult`. Service trả về `Result<TransactionSearchResultDto>`.
    - [x] `ExportTransactions`:
        - [x] Giữ nguyên `return File(...)`, nhưng service `ExportTransactionsToExcelAsync` nên trả về `Result<byte[]>`. Controller sẽ kiểm tra `result.IsSuccess` trước khi trả về file.

### 4. PropertyAnalyticsController & IPropertyAnalyticsService [ĐÃ HOÀN THÀNH]

- **`PropertyAnalyticsController.cs`**:
    - [x] `GetPropertyAnalytics`: Áp dụng `HandleResult`. Service trả về `Result<PropertyAnalyticsDto>`.
    - [x] `GetUserPropertiesAnalytics`:
        - [x] Chuyển logic caching vào trong service.
        - [x] Áp dụng `HandleResult`. Service trả về `Result<PagedResultDto<PropertyAnalyticsDto>>`.
    - [x] `ExportPropertyAnalytics`: Giữ nguyên `return File(...)`, service trả về `Result<byte[]>`.
    - [x] `ExportUserPropertiesAnalytics`: Giữ nguyên `return File(...)`, service trả về `Result<byte[]>`.
    - [x] `LogPropertyView`: Chuyển sang `HandleResult`. Service `LogPropertyViewAsync` trả về `Result`.
    - [x] `GetPropertyHistoryStatus`: Áp dụng `HandleResult`. Service `GetPropertyHistoryStatus` trả về `Result<IEnumerable<PropertyStatusLogDto>>`.
    - [x] `LogPropertyEvent`: Chuyển sang `HandleResult`. Service `LogPropertyEngagementEventAsync` trả về `Result`.
    - [x] `LogPropertyViewFromFrontend`: Chuyển sang `HandleResult`. Service `LogPropertyViewAsync` trả về `Result`.
    - [x] `GetPropertyEngagementSummary`:
        - [x] Chuyển logic caching vào trong service.
        - [x] Áp dụng `HandleResult`. Service trả về `Result<object>`.

### 5. UserController & IUserService & IUserDashboardService [ĐÃ HOÀN THÀNH]

- **`UserController.cs`**:
    - [x] `AddUserRole`: Áp dụng `HandleResult`. Service `AddUserRoleAsync` trả về `Result<bool>` hoặc `Result`.
    - [x] `GetUser`: Áp dụng `HandleResult`. Service `GetUserByIdAsync` trả về `Result<UserDto>`.
    - [x] `GetUserDashboard`: Áp dụng `HandleResult`. Service `GetUserDashboardAsync` trả về `Result<UserDashboardDto>`.
    - [x] `GetUserWallet`: Áp dụng `HandleResult`. Service `GetUserWalletInfoAsync` trả về `Result<WalletInfoDto>`.
    - [x] `GetUserPropertyStats`: Áp dụng `HandleResult`. Service `GetUserPropertyStatsAsync` trả về `Result<PropertyStatsDto>`.
    - [x] `GetUserTransactions`: Áp dụng `HandleResult`. Service `GetUserTransactionsAsync` trả về `Result<List<WalletTransactionDto>>`.
    - [x] `GetUserRanking`: Áp dụng `HandleResult`. Service `GetUserMemberRankingInfoAsync` trả về `Result<MemberRankingDto>`.
    - [x] `GetMonthlySpending`: Áp dụng `HandleResult`. Service `GetMonthlySpendingAsync` trả về `Result<...spending data type...>`.
    - [x] `GetPropertyPerformance`: Áp dụng `HandleResult`. Service `GetPropertyPerformanceAsync` trả về `Result<...performance data type...>`.
    - [x] `DeactivateAccount`: Áp dụng `HandleResult`. Service `DeactivateUserAsync` trả về `Result`.
    - [x] `PermanentDeleteAccount`: Áp dụng `HandleResult`. Service `PermanentDeleteUserAsync` trả về `Result`.
    - [x] `GetUserTaxInfo`: Áp dụng `HandleResult`. Service `GetUserByIdAsync` trả về `Result<UserDto>`, controller sẽ map sang DTO mong muốn.
    - [x] `UpdateUserTaxInfo`: Áp dụng `HandleResult`. Service `UpdateUserTaxInfoAsync` trả về `Result`.

### 6. UserFavoritesController & IUserFavoriteService [ĐÃ HOÀN THÀNH]

- **`UserFavoritesController.cs`**:
    - [x] `GetUserFavorites`: Áp dụng `HandleResult`. Service trả về `Result<IEnumerable<UserFavoriteDto>>`.
    - [x] `GetUserFavoritesWithDetails`: Áp dụng `HandleResult`. Service trả về `Result<PagedFavoriteResultDto>`.
    - [x] `AddToFavorites`: Áp dụng `HandleResult`. Service trả về `Result`.
    - [x] `RemoveFromFavorites`: Áp dụng `HandleResult`. Service trả về `Result`.
    - [x] `CheckFavoriteStatus`: Áp dụng `HandleResult`. Service trả về `Result<List<FavoriteStatusDto>>`.
    - [x] `GetFavoritesCount`: Áp dụng `HandleResult`. Service trả về `Result<int>` hoặc `Result<FavoriteCountDto>`.