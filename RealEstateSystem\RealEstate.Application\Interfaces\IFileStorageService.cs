using Microsoft.AspNetCore.Http;
using Shared.Results;

namespace RealEstate.Application.Interfaces
{
    /// <summary>
    /// Interface for file storage operations, supporting both local and cloud storage implementations
    /// </summary>
    public interface IFileStorageService
    {
        /// <summary>
        /// Upload a file to storage
        /// </summary>
        /// <param name="file">The file to upload</param>
        /// <param name="folder">The folder path where the file should be stored</param>
        /// <param name="fileName">The desired file name</param>
        /// <returns>The full path/URL where the file was stored</returns>
        Task<Result<string>> UploadFileAsync(IFormFile file, string folder, string fileName);

        /// <summary>
        /// Move a file from one location to another
        /// </summary>
        /// <param name="sourcePath">Source file path</param>
        /// <param name="destinationPath">Destination file path</param>
        /// <param name="overwrite">Whether to overwrite if destination exists</param>
        /// <returns>Success result</returns>
        Task<Result> MoveFileAsync(string sourcePath, string destinationPath, bool overwrite = true);

        /// <summary>
        /// Delete a file from storage
        /// </summary>
        /// <param name="filePath">Path to the file to delete</param>
        /// <returns>Success result</returns>
        Task<Result> DeleteFileAsync(string filePath);

        /// <summary>
        /// Check if a file exists in storage
        /// </summary>
        /// <param name="filePath">Path to check</param>
        /// <returns>True if file exists</returns>
        Task<bool> FileExistsAsync(string filePath);

        /// <summary>
        /// Ensure a directory/folder exists in storage
        /// </summary>
        /// <param name="folderPath">Path to the folder</param>
        /// <returns>Success result</returns>
        Task<Result> EnsureFolderExistsAsync(string folderPath);

        /// <summary>
        /// Generate a public URL for accessing a file
        /// </summary>
        /// <param name="filePath">Internal file path</param>
        /// <param name="scheme">HTTP scheme (http/https)</param>
        /// <param name="host">Host name</param>
        /// <returns>Public URL for the file</returns>
        string GeneratePublicUrl(string filePath, string scheme, string host);

        /// <summary>
        /// Get the appropriate folder path for property images
        /// </summary>
        /// <param name="propertyId">Property ID (null for temporary uploads)</param>
        /// <returns>Folder path</returns>
        string GetPropertyImageFolder(Guid? propertyId);

        /// <summary>
        /// Generate a temporary file path for processing
        /// </summary>
        /// <param name="folder">Base folder</param>
        /// <param name="fileId">File identifier</param>
        /// <param name="extension">File extension</param>
        /// <returns>Temporary file path</returns>
        string GenerateTempFilePath(string folder, Guid fileId, string extension);
    }
}
